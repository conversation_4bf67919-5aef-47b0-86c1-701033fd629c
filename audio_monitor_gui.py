#!/usr/bin/env python3
"""
音频监听工具GUI版本
提供实时音频监控、可视化显示和设备管理功能
"""

import sys
import os
import time
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QComboBox, QSlider, QProgressBar, QGroupBox,
    QFormLayout, QSpinBox, QDoubleSpinBox, QCheckBox, QTextEdit,
    QTabWidget, QGridLayout, QFrame, QSplitter
)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSettings
from PyQt5.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap
from PyQt5.QtMultimedia import QAudioDeviceInfo, QAudio

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("⚠️ PyAudio未安装，部分功能将不可用")

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.animation as animation
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib未安装，波形显示功能将不可用")


class AudioLevelWidget(QWidget):
    """音频电平显示控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(200, 30)
        self.setMaximumHeight(30)
        self.level = -80.0  # dB
        self.threshold = -30.0  # dB
        
    def set_level(self, level_db):
        """设置音频电平"""
        self.level = max(-80.0, min(0.0, level_db))
        self.update()
        
    def set_threshold(self, threshold_db):
        """设置阈值"""
        self.threshold = threshold_db
        self.update()
        
    def paintEvent(self, event):
        """绘制音频电平条"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 背景
        painter.fillRect(self.rect(), QColor(40, 40, 40))
        
        # 计算电平条宽度 (-80dB到0dB)
        level_width = int((self.level + 80) / 80 * self.width())
        
        # 绘制电平条
        if level_width > 0:
            # 根据电平选择颜色
            if self.level > -6:  # 红色区域
                color = QColor(255, 0, 0)
            elif self.level > -18:  # 黄色区域
                color = QColor(255, 255, 0)
            else:  # 绿色区域
                color = QColor(0, 255, 0)
                
            painter.fillRect(0, 0, level_width, self.height(), color)
        
        # 绘制阈值线
        threshold_x = int((self.threshold + 80) / 80 * self.width())
        if 0 <= threshold_x <= self.width():
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.drawLine(threshold_x, 0, threshold_x, self.height())
        
        # 绘制刻度
        painter.setPen(QPen(QColor(200, 200, 200), 1))
        for db in [-60, -40, -20, -10, -6, 0]:
            x = int((db + 80) / 80 * self.width())
            if 0 <= x <= self.width():
                painter.drawLine(x, self.height() - 5, x, self.height())


class AudioMonitorThread(QThread):
    """音频监控线程"""
    level_updated = pyqtSignal(float)  # 音频电平更新信号
    data_updated = pyqtSignal(np.ndarray)  # 音频数据更新信号
    
    def __init__(self, device_index=None, parent=None):
        super().__init__(parent)
        self.device_index = device_index
        self.running = False
        self.chunk_size = 1024
        self.sample_rate = 44100
        self.stream = None
        
    def run(self):
        """线程主循环"""
        if not PYAUDIO_AVAILABLE:
            print("❌ PyAudio不可用，无法启动音频监控")
            return
            
        self.running = True
        
        try:
            # 初始化PyAudio
            pa = pyaudio.PyAudio()
            
            # 打开音频流
            self.stream = pa.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=self.sample_rate,
                input=True,
                input_device_index=self.device_index,
                frames_per_buffer=self.chunk_size
            )
            
            print(f"🎵 音频监控已启动，设备索引: {self.device_index}")
            
            while self.running:
                try:
                    # 读取音频数据
                    data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                    audio_data = np.frombuffer(data, dtype=np.int16)
                    
                    # 计算RMS电平
                    if len(audio_data) > 0:
                        rms = np.sqrt(np.mean(audio_data.astype(np.float64) ** 2))
                        if rms > 0:
                            db_level = 20 * np.log10(rms / 32767.0)
                        else:
                            db_level = -80.0
                        
                        # 发送信号
                        self.level_updated.emit(db_level)
                        self.data_updated.emit(audio_data)
                    
                    self.msleep(50)  # 50ms更新间隔
                    
                except Exception as e:
                    if self.running:
                        print(f"❌ 音频数据读取错误: {e}")
                    break
                    
        except Exception as e:
            print(f"❌ 音频监控初始化失败: {e}")
        finally:
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
            if 'pa' in locals():
                pa.terminate()
            print("🛑 音频监控已停止")
    
    def stop(self):
        """停止监控"""
        self.running = False


class WaveformWidget(QWidget):
    """波形显示控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(400, 200)
        self.audio_data = np.zeros(1024)
        self.sample_rate = 44100
        
        if MATPLOTLIB_AVAILABLE:
            self.setup_matplotlib()
        else:
            self.setup_simple_display()
    
    def setup_matplotlib(self):
        """设置matplotlib显示"""
        self.figure = Figure(figsize=(8, 3), facecolor='black')
        self.canvas = FigureCanvas(self.figure)
        
        layout = QVBoxLayout(self)
        layout.addWidget(self.canvas)
        
        self.ax = self.figure.add_subplot(111, facecolor='black')
        self.ax.set_xlim(0, len(self.audio_data))
        self.ax.set_ylim(-32768, 32767)
        self.ax.set_xlabel('样本', color='white')
        self.ax.set_ylabel('幅度', color='white')
        self.ax.tick_params(colors='white')
        
        self.line, = self.ax.plot(self.audio_data, color='lime', linewidth=1)
        self.figure.tight_layout()
    
    def setup_simple_display(self):
        """设置简单显示"""
        layout = QVBoxLayout(self)
        label = QLabel("波形显示需要安装matplotlib库")
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet("color: gray; font-size: 14px;")
        layout.addWidget(label)
    
    def update_waveform(self, audio_data):
        """更新波形显示"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        self.audio_data = audio_data
        self.line.set_ydata(audio_data)
        self.canvas.draw()


class AudioMonitorGUI(QMainWindow):
    """音频监控GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("音频监听工具 v1.0")
        self.setGeometry(100, 100, 800, 600)
        
        # 初始化变量
        self.monitor_thread = None
        self.audio_devices = []
        self.settings = QSettings("AudioMonitor", "Settings")
        
        # 初始化界面
        self.init_ui()
        self.init_audio_devices()
        self.load_settings()
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 监控标签页
        monitor_tab = self.create_monitor_tab()
        tab_widget.addTab(monitor_tab, "🎵 音频监控")
        
        # 设置标签页
        settings_tab = self.create_settings_tab()
        tab_widget.addTab(settings_tab, "⚙️ 设置")
        
        # 状态栏
        self.statusBar().showMessage("就绪")
    
    def create_monitor_tab(self):
        """创建监控标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 设备选择区域
        device_group = QGroupBox("音频设备")
        device_layout = QFormLayout(device_group)
        
        self.device_combo = QComboBox()
        self.device_combo.currentIndexChanged.connect(self.on_device_changed)
        device_layout.addRow("输入设备:", self.device_combo)
        
        self.start_button = QPushButton("开始监控")
        self.start_button.clicked.connect(self.toggle_monitoring)
        device_layout.addRow("", self.start_button)
        
        layout.addWidget(device_group)
        
        # 电平显示区域
        level_group = QGroupBox("音频电平")
        level_layout = QVBoxLayout(level_group)
        
        self.level_widget = AudioLevelWidget()
        level_layout.addWidget(self.level_widget)
        
        self.level_label = QLabel("电平: -80.0 dB")
        level_layout.addWidget(self.level_label)
        
        layout.addWidget(level_group)
        
        # 波形显示区域
        waveform_group = QGroupBox("波形显示")
        waveform_layout = QVBoxLayout(waveform_group)
        
        self.waveform_widget = WaveformWidget()
        waveform_layout.addWidget(self.waveform_widget)
        
        layout.addWidget(waveform_group)
        
        return tab
    
    def create_settings_tab(self):
        """创建设置标签页"""
        tab = QWidget()
        layout = QFormLayout(tab)
        
        # 阈值设置
        self.threshold_spin = QDoubleSpinBox()
        self.threshold_spin.setRange(-80.0, 0.0)
        self.threshold_spin.setValue(-30.0)
        self.threshold_spin.setSuffix(" dB")
        self.threshold_spin.valueChanged.connect(self.on_threshold_changed)
        layout.addRow("检测阈值:", self.threshold_spin)
        
        # 更新间隔
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(10, 1000)
        self.interval_spin.setValue(50)
        self.interval_spin.setSuffix(" ms")
        layout.addRow("更新间隔:", self.interval_spin)
        
        return tab

    def init_audio_devices(self):
        """初始化音频设备列表"""
        self.audio_devices.clear()

        if not PYAUDIO_AVAILABLE:
            self.device_combo.addItem("PyAudio不可用", None)
            return

        try:
            pa = pyaudio.PyAudio()

            # 获取设备数量
            device_count = pa.get_device_count()

            for i in range(device_count):
                try:
                    device_info = pa.get_device_info_by_index(i)

                    # 只添加输入设备
                    if device_info['maxInputChannels'] > 0:
                        device_name = device_info['name']
                        self.audio_devices.append({
                            'index': i,
                            'name': device_name,
                            'info': device_info
                        })
                        self.device_combo.addItem(device_name, i)

                except Exception as e:
                    print(f"获取设备 {i} 信息失败: {e}")

            pa.terminate()
            print(f"✅ 找到 {len(self.audio_devices)} 个音频输入设备")

        except Exception as e:
            print(f"❌ 初始化音频设备失败: {e}")
            self.device_combo.addItem("设备初始化失败", None)

    def on_device_changed(self):
        """设备选择改变"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.stop_monitoring()

    def on_threshold_changed(self, value):
        """阈值改变"""
        self.level_widget.set_threshold(value)

    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        device_index = self.device_combo.currentData()
        if device_index is None:
            self.statusBar().showMessage("请选择有效的音频设备")
            return

        # 创建监控线程
        self.monitor_thread = AudioMonitorThread(device_index, self)
        self.monitor_thread.level_updated.connect(self.on_level_updated)
        self.monitor_thread.data_updated.connect(self.on_data_updated)
        self.monitor_thread.finished.connect(self.on_monitoring_finished)

        # 启动监控
        self.monitor_thread.start()

        # 更新界面
        self.start_button.setText("停止监控")
        self.start_button.setStyleSheet("background-color: #ff4444;")
        self.statusBar().showMessage(f"正在监控: {self.device_combo.currentText()}")

    def stop_monitoring(self):
        """停止监控"""
        if self.monitor_thread:
            self.monitor_thread.stop()
            self.monitor_thread.wait(3000)  # 等待最多3秒

        self.on_monitoring_finished()

    def on_monitoring_finished(self):
        """监控结束处理"""
        self.start_button.setText("开始监控")
        self.start_button.setStyleSheet("")
        self.statusBar().showMessage("监控已停止")
        self.level_widget.set_level(-80.0)
        self.level_label.setText("电平: -80.0 dB")

    def on_level_updated(self, level_db):
        """音频电平更新"""
        self.level_widget.set_level(level_db)
        self.level_label.setText(f"电平: {level_db:.1f} dB")

        # 检查是否超过阈值
        threshold = self.threshold_spin.value()
        if level_db > threshold:
            self.level_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.level_label.setStyleSheet("")

    def on_data_updated(self, audio_data):
        """音频数据更新"""
        self.waveform_widget.update_waveform(audio_data)

    def load_settings(self):
        """加载设置"""
        try:
            # 加载阈值设置
            threshold = self.settings.value("threshold", -30.0, type=float)
            self.threshold_spin.setValue(threshold)
            self.level_widget.set_threshold(threshold)

            # 加载设备选择
            device_name = self.settings.value("device", "", type=str)
            if device_name:
                index = self.device_combo.findText(device_name)
                if index >= 0:
                    self.device_combo.setCurrentIndex(index)

        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            self.settings.setValue("threshold", self.threshold_spin.value())
            self.settings.setValue("device", self.device_combo.currentText())
        except Exception as e:
            print(f"保存设置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.stop_monitoring()
        self.save_settings()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("音频监听工具")
    app.setApplicationVersion("1.0")

    # 设置应用样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2b2b2b;
            color: white;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        QComboBox, QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #666;
            border-radius: 3px;
            padding: 5px;
            color: white;
        }
        QLabel {
            color: white;
        }
        QTabWidget::pane {
            border: 1px solid #555;
            background-color: #2b2b2b;
        }
        QTabBar::tab {
            background-color: #404040;
            color: white;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #4CAF50;
        }
    """)

    # 创建主窗口
    window = AudioMonitorGUI()
    window.show()

    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
